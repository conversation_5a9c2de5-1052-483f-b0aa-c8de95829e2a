import { useState, useEffect, useCallback, useRef } from 'react';
import urlRefreshService from '../services/urlRefreshService';

/**
 * Custom hook for managing signed URLs with automatic refresh
 * @param {string} originalUrl - Original file URL
 * @param {Object} options - Configuration options
 * @returns {Object} - { signedUrl, isLoading, error, refresh, needsRefresh }
 */
export const useSignedUrl = (originalUrl, options = {}) => {
  const {
    type = 'content',
    autoRefresh = true,
    refreshInterval = 5 * 60 * 1000, // 5 minutes
    onError = null,
    onRefresh = null
  } = options;

  const [signedUrl, setSignedUrl] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [needsRefresh, setNeedsRefresh] = useState(false);

  const refreshIntervalRef = useRef(null);
  const mountedRef = useRef(true);

  // Manual refresh function
  const refresh = useCallback(async (forceRefresh = false) => {
    if (!originalUrl || !mountedRef.current) return;

    setIsLoading(true);
    setError(null);

    try {
      const newSignedUrl = await urlRefreshService.getSignedUrl(originalUrl, type, forceRefresh);
      
      if (mountedRef.current) {
        setSignedUrl(newSignedUrl);
        setNeedsRefresh(false);
        
        if (onRefresh) {
          onRefresh(newSignedUrl, originalUrl);
        }
      }
    } catch (err) {
      console.error('[useSignedUrl] Error refreshing URL:', err);
      
      if (mountedRef.current) {
        setError(err.message || 'Failed to refresh URL');
        
        if (onError) {
          onError(err, originalUrl);
        }
      }
    } finally {
      if (mountedRef.current) {
        setIsLoading(false);
      }
    }
  }, [originalUrl, type, onError, onRefresh]);

  // Check if refresh is needed
  const checkRefreshNeeded = useCallback(() => {
    if (!originalUrl) return false;
    
    const needed = urlRefreshService.needsRefresh(originalUrl, type);
    setNeedsRefresh(needed);
    return needed;
  }, [originalUrl, type]);

  // Initial load
  useEffect(() => {
    if (originalUrl) {
      refresh(false);
    }
  }, [originalUrl, refresh]);

  // Set up automatic refresh interval
  useEffect(() => {
    if (!autoRefresh || !originalUrl) return;

    const intervalId = setInterval(() => {
      if (checkRefreshNeeded()) {
        refresh(false);
      }
    }, refreshInterval);

    refreshIntervalRef.current = intervalId;

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [autoRefresh, originalUrl, refreshInterval, checkRefreshNeeded, refresh]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, []);

  return {
    signedUrl,
    isLoading,
    error,
    refresh: () => refresh(true),
    needsRefresh,
    checkRefreshNeeded
  };
};

/**
 * Hook for managing multiple signed URLs
 * @param {Array<string>} urls - Array of original URLs
 * @param {Object} options - Configuration options
 * @returns {Object} - { signedUrls, isLoading, errors, refreshAll }
 */
export const useMultipleSignedUrls = (urls = [], options = {}) => {
  const {
    type = 'content',
    autoRefresh = true,
    refreshInterval = 5 * 60 * 1000
  } = options;

  const [signedUrls, setSignedUrls] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const mountedRef = useRef(true);
  const refreshIntervalRef = useRef(null);

  // Refresh all URLs
  const refreshAll = useCallback(async (forceRefresh = false) => {
    if (!urls.length || !mountedRef.current) return;

    setIsLoading(true);
    setErrors({});

    try {
      const results = await urlRefreshService.refreshMultipleUrls(urls, type);
      
      if (mountedRef.current) {
        setSignedUrls(prev => ({ ...prev, ...results }));
      }
    } catch (err) {
      console.error('[useMultipleSignedUrls] Error refreshing URLs:', err);
      
      if (mountedRef.current) {
        setErrors(prev => ({ ...prev, general: err.message }));
      }
    } finally {
      if (mountedRef.current) {
        setIsLoading(false);
      }
    }
  }, [urls, type]);

  // Refresh individual URL
  const refreshSingle = useCallback(async (url, forceRefresh = false) => {
    if (!url || !mountedRef.current) return;

    try {
      const signedUrl = await urlRefreshService.getSignedUrl(url, type, forceRefresh);
      
      if (mountedRef.current) {
        setSignedUrls(prev => ({ ...prev, [url]: signedUrl }));
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[url];
          return newErrors;
        });
      }
    } catch (err) {
      console.error(`[useMultipleSignedUrls] Error refreshing URL ${url}:`, err);
      
      if (mountedRef.current) {
        setErrors(prev => ({ ...prev, [url]: err.message }));
      }
    }
  }, [type]);

  // Initial load
  useEffect(() => {
    if (urls.length > 0) {
      refreshAll(false);
    }
  }, [urls, refreshAll]);

  // Set up automatic refresh
  useEffect(() => {
    if (!autoRefresh || !urls.length) return;

    const intervalId = setInterval(() => {
      // Check which URLs need refresh
      const urlsNeedingRefresh = urls.filter(url => 
        urlRefreshService.needsRefresh(url, type)
      );

      if (urlsNeedingRefresh.length > 0) {
        // Refresh only URLs that need it
        urlsNeedingRefresh.forEach(url => refreshSingle(url, false));
      }
    }, refreshInterval);

    refreshIntervalRef.current = intervalId;

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [autoRefresh, urls, refreshInterval, type, refreshSingle]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, []);

  return {
    signedUrls,
    isLoading,
    errors,
    refreshAll: () => refreshAll(true),
    refreshSingle: (url) => refreshSingle(url, true)
  };
};

/**
 * Hook for handling video/media URLs with retry logic
 * @param {string} mediaUrl - Media file URL
 * @param {Object} options - Configuration options
 * @returns {Object} - Enhanced signed URL management for media
 */
export const useMediaSignedUrl = (mediaUrl, options = {}) => {
  const {
    onLoadError = null,
    maxRetries = 3,
    retryDelay = 2000,
    ...otherOptions
  } = options;

  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  const {
    signedUrl,
    isLoading,
    error,
    refresh,
    needsRefresh
  } = useSignedUrl(mediaUrl, {
    ...otherOptions,
    onError: (err, url) => {
      console.error('[useMediaSignedUrl] URL error:', err);
      
      // Trigger retry if we haven't exceeded max retries
      if (retryCount < maxRetries) {
        setIsRetrying(true);
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          refresh();
          setIsRetrying(false);
        }, retryDelay * (retryCount + 1)); // Exponential backoff
      } else if (onLoadError) {
        onLoadError(err, url);
      }
    }
  });

  // Reset retry count when URL changes or refresh succeeds
  useEffect(() => {
    if (signedUrl && !error) {
      setRetryCount(0);
      setIsRetrying(false);
    }
  }, [signedUrl, error]);

  // Reset retry count when media URL changes
  useEffect(() => {
    setRetryCount(0);
    setIsRetrying(false);
  }, [mediaUrl]);

  return {
    signedUrl,
    isLoading: isLoading || isRetrying,
    error,
    refresh,
    needsRefresh,
    retryCount,
    isRetrying,
    canRetry: retryCount < maxRetries
  };
};

export default useSignedUrl;
