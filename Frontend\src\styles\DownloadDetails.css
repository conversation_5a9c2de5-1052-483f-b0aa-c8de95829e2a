.DownloadDetails {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: var(--heading5);
}

.DownloadDetails .DownloadDetails__wrapper {
  border-radius: var(--border-radius);

  overflow-x: scroll;
}

.DownloadDetails .DownloadDetails__wrapper::-webkit-scrollbar {
  display: none;
}

/* Header with seller layout pattern */
.DownloadDetails .DownloadDetails__wrapper .bordrdiv {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.DownloadDetails .DownloadDetails__back-btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  background: none;
  border: none;
  color: #0a0033;
  font-size: var(--basefont);
  cursor: pointer;
  padding: var(--smallfont) var(--basefont);
  font-weight: 600;
  border-radius: var(--border-radius);
  background-color: #fddcdc;
  border-top-left-radius: 6px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 0px;
  clip-path: polygon(0 0, 95% 0, 100% 100%, 0% 100%);
}

/* Utility class for margin-bottom */
.DownloadDetails .mb-30 {
  margin-bottom: 30px;
}
.DownloadDetails .DownloadDetails__content-main {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  justify-content: space-between;
}
/* Content Info */
.DownloadDetails .DownloadDetails__content-info {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
  justify-content: space-between;
}

.DownloadDetails .DownloadDetails__content-image {
  width: 80px;
  height: 60px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.DownloadDetails .DownloadDetails__content-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.DownloadDetails .DownloadDetails__content-details {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.DownloadDetails .DownloadDetails__content-title {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.DownloadDetails .DownloadDetails__content-coach {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0;
}
.DownloadDetails .DownloadDetails__card {
  background-color: #ffffff; /* White background */
  border-radius: 16px; /* Figma border radius */
  padding: 24px; /* Figma padding */
  display: flex;
  flex-direction: column;
  gap: 24px; /* Figma gap */
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.05); /* Subtle shadow */
  border: 1px solid #e0e0e0; /* Light gray border as in Figma */
}

.DownloadDetails .DownloadDetails__card-section {
  display: grid;
  gap: 20px; /* Adjusted gap */
}

.DownloadDetails .DownloadDetails__card-section--order {
  /* Adjusted for 2 main content blocks + 1 vertical line */
  grid-template-columns: 1fr;
  align-items: flex-start;
  /* margin-bottom: 24px; */ /* Gap is handled by parent */
}

.DownloadDetails .DownloadDetails__card-section--details {
  grid-template-columns: 1fr auto 1fr; /* Customer details | vertical line | Payment details */
  align-items: flex-start;
}

.DownloadDetails .DownloadDetails__card-info-block {
  display: flex;
  flex-direction: column;
  gap: 12px; /* Figma uses a slightly larger gap between subtitle and details */
  justify-content: flex-start;
}

.DownloadDetails .DownloadDetails__card-subtitle {
  font-size: 16px; /* Figma font size */
  font-weight: 600; /* Figma font weight */
  color: #0a0033; /* Figma text color */
  margin-bottom: 4px; /* Reduced margin as gap is handled by parent */
}

.DownloadDetails .DownloadDetails__card-detail {
  display: flex;
  gap: 8px;
  align-items: flex-start; /* Align items to the start for multi-line values */
  font-size: 14px;
  flex-wrap: wrap; /* Figma base font size for details */
}

.DownloadDetails .DownloadDetails__card-detail-label {
  font-size: 14px; /* Figma font size */
  color: #555555; /* Figma label color (darker gray) */
  font-weight: 400; /* Figma font weight */
  min-width: 90px; /* Adjusted min-width */
  flex-shrink: 0; /* Prevent shrinking */
}

.DownloadDetails .DownloadDetails__card-detail-value {
  font-size: 14px; /* Figma font size */
  color: #0a0033; /* Figma value color */
  font-weight: 500; /* Figma font weight */
  text-align: left;
}

.DownloadDetails .DownloadDetails__card-payment {
  display: flex;
  align-items: center;
  gap: 8px; /* Figma gap */
  /* margin-top: 8px; */ /* Gap handled by parent */
}

.DownloadDetails .DownloadDetails__card-payment-icon {
  width: 30px; /* Figma icon size */
  height: 18px; /* Figma icon size */
  object-fit: contain;
}

.DownloadDetails .DownloadDetails__card-payment-value {
  font-size: 14px; /* Figma font size */
  color: #0a0033; /* Figma value color */
  font-weight: 500; /* Figma font weight */
}

.DownloadDetails .vertical-line {
  width: 1px;
  display: flex;
  background-color: #e0e0e0; /* Figma divider color */
  min-height: 40px; /* Keep a minimum height */
  height: auto; /* Allow it to stretch based on content */
  align-self: stretch;
  margin: 0 20px; /* Figma margin for vertical line */
}

.DownloadDetails .DownloadDetails__card-divider {
  height: 1px;
  background: #e0e0e0; /* Figma divider color */
  width: 100%;
  margin: 0; /* Margin handled by parent gap */
}

/* Section Titles */
.DownloadDetails .DownloadDetails__section-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 var(--basefont) 0;
}

/* Order Information - Figma Design Match */
.DownloadDetails .DownloadDetails__order-info {
  margin-bottom: var(--heading5);
}

.DownloadDetails .DownloadDetails__order-info-container {
  display: grid;
  grid-template-columns: 1fr auto 1fr auto 1fr auto 1fr;
  align-items: center;

  border-radius: var(--border-radius);
}

.DownloadDetails .DownloadDetails__order-info-item {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
  text-align: center;
  padding: 0 var(--smallfont);
}

.DownloadDetails .DownloadDetails__order-info-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.DownloadDetails .DownloadDetails__order-info-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
}

.DownloadDetails .DownloadDetails__order-info-divider {
  width: 1px;
  height: 40px;
  background-color: var(--light-gray);
  margin: 0 var(--smallfont);
}
.DownloadDetails .outerdivmain {
  display: grid !important;
  grid-template-columns: 1fr auto 1fr;
  gap: 1rem;
}

/* Customer and Payment Details Grid */
.DownloadDetails .DownloadDetails__details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--heading5);
  margin-bottom: var(--heading5);
}

.DownloadDetails .DownloadDetails__customer-details,
.DownloadDetails .DownloadDetails__payment-details {
  padding: var(--basefont);
  border-radius: var(--border-radius);
}

.DownloadDetails .DownloadDetails__details-content {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.DownloadDetails .DownloadDetails__detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.DownloadDetails .DownloadDetails__detail-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.DownloadDetails .DownloadDetails__detail-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

/* Payment Method */
.DownloadDetails .DownloadDetails__payment-method {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.DownloadDetails .DownloadDetails__payment-icon {
  width: 32px;
  height: 20px;
  object-fit: contain;
}

.DownloadDetails .DownloadDetails__payment-text {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

/* Video Section */
.DownloadDetails .DownloadDetails__video-section {
  margin-bottom: var(--heading5);
}

.DownloadDetails .DownloadDetails__video-container {
  width: 100%;
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.DownloadDetails .DownloadDetails__video-player {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: var(--border-radius-medium);
  overflow: hidden;
  background-color: var(--black);
}

.DownloadDetails .DownloadDetails__video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Document Viewer Styles */
.DownloadDetails .DownloadDetails__document-viewer,
.DownloadDetails .DownloadDetails__pdf-viewer {
  position: relative;
  width: 100%;
  height: 950px;
  background-color: var(--white);
  border-radius: var(--border-radius-medium);
  overflow: hidden;
}

.DownloadDetails .DownloadDetails__pdf-element {
  width: 100%;
  height: 100%;
  border: none;
  background-color: var(--white);
}

/* Loading State */
.DownloadDetails .DownloadDetails__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: var(--basefont);
  color: var(--dark-gray);
}

.DownloadDetails .DownloadDetails__loading .spinning {
  animation: spin 1s linear infinite;
  font-size: 1.5rem;
  color: var(--btn-color);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.DownloadDetails .DownloadDetails__pdf-fallback {
  position: absolute;
  bottom: var(--basefont);
  right: var(--basefont);
  background-color: rgba(0, 0, 0, 0.8);
  color: var(--white);
  padding: var(--smallfont) var(--basefont);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
}

.DownloadDetails .DownloadDetails__pdf-fallback a {
  color: var(--btn-color);
  text-decoration: none;
  margin-left: var(--extrasmallfont);
}

.DownloadDetails .DownloadDetails__pdf-fallback a:hover {
  text-decoration: underline;
}

/* Audio Player Styles */
.DownloadDetails .DownloadDetails__audio-player {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--basefont);
  padding: var(--heading5);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius-medium);
}

.DownloadDetails .DownloadDetails__audio-element {
  width: 100%;
  max-width: 400px;
}

.DownloadDetails .DownloadDetails__audio-info {
  text-align: center;
}

.DownloadDetails .DownloadDetails__audio-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0 0 var(--extrasmallfont) 0;
}

.DownloadDetails .DownloadDetails__audio-coach {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0;
}

/* Image Viewer Styles */
.DownloadDetails .DownloadDetails__image-viewer {
  position: relative;
  width: 100%;
  max-height: 400px;
  border-radius: var(--border-radius-medium);
  overflow: hidden;
}

.DownloadDetails .DownloadDetails__image-element {
  width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: cover;
}

.DownloadDetails .DownloadDetails__image-title-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: var(--heading5) var(--basefont) var(--basefont);
}

.DownloadDetails .DownloadDetails__image-title {
  color: var(--white);
  font-size: var(--heading6);
  font-weight: 600;
  margin: 0;
  text-align: center;
}

/* File Preview Styles */
.DownloadDetails .DownloadDetails__file-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--basefont);
  padding: var(--heading4);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius-medium);
  text-align: center;
}

.DownloadDetails .DownloadDetails__file-icon-large {
  font-size: 4rem;
  color: var(--btn-color);
}

.DownloadDetails .DownloadDetails__file-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0;
}

.DownloadDetails .DownloadDetails__file-type {
  font-size: var(--basefont);
  color: var(--dark-gray);
  margin: 0;
}

.DownloadDetails .DownloadDetails__preview-download-btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--basefont) var(--heading5);
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.DownloadDetails .DownloadDetails__preview-download-btn:hover {
  background-color: var(--primary-color);
  transform: scale(1.02);
}

.DownloadDetails .DownloadDetails__preview-download-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.DownloadDetails .DownloadDetails__video-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.DownloadDetails .DownloadDetails__play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.DownloadDetails .DownloadDetails__play-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: rgba(238, 52, 37, 0.9);
  border: none;
  border-radius: 50%;
  color: var(--white);
  font-size: var(--heading4);
  cursor: pointer;
  transition: all 0.3s ease;
}

.DownloadDetails .DownloadDetails__play-btn:hover {
  background-color: var(--btn-color);
  transform: scale(1.1);
}

.DownloadDetails .DownloadDetails__video-title-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: var(--heading5) var(--basefont) var(--basefont);
}

.DownloadDetails .DownloadDetails__video-title {
  color: var(--white);
  font-size: var(--heading5);
  font-weight: 600;
  margin: 0;
  text-align: center;
}

/* Description */
.DownloadDetails .DownloadDetails__description-section {
  margin-bottom: var(--heading5);
}

.DownloadDetails .DownloadDetails__description-text {
  font-size: var(--basefont);
  line-height: 1.6;
  color: var(--text-color);
  margin: 0 0 var(--basefont) 0;
}

.DownloadDetails .DownloadDetails__description-text p {
  margin: 0 0 var(--smallfont) 0;
}

.DownloadDetails .DownloadDetails__description-text ul {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--smallfont) 0;
}

.DownloadDetails .DownloadDetails__description-text li {
  position: relative;
  padding-left: var(--heading5);
  margin-bottom: var(--extrasmallfont);
}

.DownloadDetails .DownloadDetails__description-text li::before {
  content: "•";
  color: var(--btn-color);
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 0;
}

/* Strategic Content */
.DownloadDetails .DownloadDetails__strategic-content {
  margin-top: var(--heading5);
  padding: var(--basefont);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--btn-color);
}

.DownloadDetails .DownloadDetails__strategic-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0 0 var(--basefont) 0;
}

.DownloadDetails .DownloadDetails__strategic-text {
  font-size: var(--basefont);
  line-height: 1.6;
  color: var(--text-color);
}

.DownloadDetails .DownloadDetails__strategic-text p {
  margin: 0 0 var(--smallfont) 0;
}

.DownloadDetails .DownloadDetails__strategic-text ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.DownloadDetails .DownloadDetails__strategic-text li {
  position: relative;
  padding-left: var(--heading5);
  margin-bottom: var(--extrasmallfont);
}

.DownloadDetails .DownloadDetails__strategic-text li::before {
  content: "•";
  color: var(--btn-color);
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 0;
}

.DownloadDetails .DownloadDetails__strategic-text strong {
  color: var(--secondary-color);
  font-weight: 600;
}

/* Error State */
.DownloadDetails .DownloadDetails__error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--error-color);
  font-size: var(--heading6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .DownloadDetails .DownloadDetails__order-info-container {
    grid-template-columns: 1fr auto 1fr;
    gap: var(--smallfont);
  }
  .DownloadDetails .outerdivmain {
    display: grid !important;
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  .DownloadDetails .DownloadDetails__order-info-item:nth-child(5),
  .DownloadDetails .DownloadDetails__order-info-divider:nth-child(4),
  .DownloadDetails .DownloadDetails__order-info-item:nth-child(7),
  .DownloadDetails .DownloadDetails__order-info-divider:nth-child(6) {
    display: none;
  }
  .DownloadDetails .DownloadDetails__card-section--details {
    grid-template-columns: 1fr; /* Customer details | vertical line | Payment details */
    align-items: flex-start;
  }
  .DownloadDetails .DownloadDetails__details-grid {
    grid-template-columns: 1fr;
    gap: var(--basefont);
  }

  .DownloadDetails .DownloadDetails__video-player {
    height: 250px;
  }

  .DownloadDetails .DownloadDetails__document-viewer,
  .DownloadDetails .DownloadDetails__pdf-viewer {
    height: 300px;
  }

  .DownloadDetails .DownloadDetails__image-viewer {
    max-height: 250px;
  }

  .DownloadDetails .DownloadDetails__file-preview {
    padding: var(--heading5);
  }

  .DownloadDetails .DownloadDetails__file-icon-large {
    font-size: 3rem;
  }

  .DownloadDetails .DownloadDetails__play-btn {
    width: 60px;
    height: 60px;
    font-size: var(--heading5);
  }
  .DownloadDetails .vertical-line {
    display: none; /* Figma margin for vertical line */
  }
}

@media (max-width: 480px) {
  .DownloadDetails .DownloadDetails__order-info-container {
    grid-template-columns: 1fr;
    gap: var(--basefont);
  }

  .DownloadDetails .DownloadDetails__order-info-divider {
    display: none;
  }

  .DownloadDetails .DownloadDetails__order-info-item {
    text-align: left;
    padding: var(--smallfont) 0;
    border-bottom: 1px solid var(--light-gray);
  }

  .DownloadDetails .DownloadDetails__order-info-item:last-child {
    border-bottom: none;
  }
  .DownloadDetails .DownloadDetails__content-main {
    flex-direction: column;
    align-items: flex-start;
  }
  .DownloadDetails .DownloadDetails__content-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .DownloadDetails .DownloadDetails__video-player {
    height: 200px;
  }

  .DownloadDetails .DownloadDetails__document-viewer,
  .DownloadDetails .DownloadDetails__pdf-viewer {
    height: 250px;
  }

  .DownloadDetails .DownloadDetails__image-viewer {
    max-height: 200px;
  }

  .DownloadDetails .DownloadDetails__file-preview {
    padding: var(--basefont);
  }

  .DownloadDetails .DownloadDetails__file-icon-large {
    font-size: 2.5rem;
  }

  .DownloadDetails .DownloadDetails__audio-player {
    padding: var(--basefont);
  }

  .DownloadDetails .DownloadDetails__strategic-content {
    padding: var(--smallfont);
    margin-top: var(--basefont);
  }

  .DownloadDetails .DownloadDetails__pdf-fallback {
    bottom: var(--smallfont);
    right: var(--smallfont);
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--extrasmallfont);
  }

  .DownloadDetails .mb-30 {
    margin-bottom: var(--basefont);
  }
}

/* Reviews Section */
.DownloadDetails__reviews-section {
  margin-top: 2rem;
  /* padding: 1.5rem; */
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.DownloadDetails__review-btn {
  padding: 0.5rem 1rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 1rem;
}

.DownloadDetails__reviews-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.DownloadDetails__review-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  display: grid;
  gap: 6px;
}

.DownloadDetails__review-header {
  display: flex;

  align-items: flex-start;
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
}

.DownloadDetails__review-user-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.DownloadDetails__review-user-info h4 {
  margin: 0;
  color: #333;
}

.DownloadDetails__rating-stars {
  display: flex;
  gap: 0.25rem;
}

.star-icon {
  color: #ffd700;
  font-size: 1.2rem;
}

.star-icon.empty {
  color: #e0e0e0;
}

.star-icon.half-filled {
  position: relative;
  color: #ffd700;
}

.DownloadDetails__review-actions {
  display: flex;
  gap: 0.5rem;
}

.DownloadDetails__review-actions button {
  padding: 0.25rem 0.75rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  background: white;
  cursor: pointer;
  font-size: 0.9rem;
}

.DownloadDetails__review-actions button:hover {
  background: var(--second-primary-color);
  color: var(--white);
}

.DownloadDetails__review-date {
  display: block;
  color: #666;
  font-size: 0.9rem;
}

.DownloadDetails__actions {
  display: flex;
  gap: 1rem;
}

.DownloadDetails__review-btn {
  background: var(--btn-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.DownloadDetails__rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.DownloadDetails__rating span {
  color: var(--text-secondary);
  font-size: 14px;
}

.reviews-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

@media (max-width: 320px) {
  .DownloadDetails__review-header {
    display: flex;
    align-items: flex-start;
    width: 100%;
    gap: 10px;
    flex-direction: column-reverse;
    justify-content: space-between;
  }
  .DownloadDetails .DownloadDetails__card {
    padding: 24px 10px;
  }
}

/* Preview Button Styles */
.DownloadDetails__section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--basefont);
  gap: var(--basefont);
}

.DownloadDetails__previewBtn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: var(--smallfont);
  white-space: nowrap;
  flex-shrink: 0;
}

.DownloadDetails__previewBtn:hover {
  transform: translateY(-1px);
}

/* Responsive adjustments for preview button */
@media (max-width: 768px) {
  .DownloadDetails__section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .DownloadDetails__previewBtn {
    align-self: flex-end;
    font-size: var(--extrasmallfont);
    padding: 6px 12px;
  }
}

@media (max-width: 480px) {
  .DownloadDetails__section-header {
    gap: 6px;
  }

  .DownloadDetails__previewBtn {
    width: 100%;
    justify-content: center;
  }
}

/* URL Refresh Functionality */
.DownloadDetails__url-error {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 12px;
  margin: 10px 0;
  text-align: center;
}

.DownloadDetails__url-error p {
  margin: 0;
  color: #856404;
  font-size: 14px;
}

.DownloadDetails__refresh-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  margin-left: 8px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.DownloadDetails__refresh-btn:hover {
  background: #0056b3;
}

.DownloadDetails__refresh-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}
